package organization

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/requests"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type OrganizationController struct {
}

func (m OrganizationController) Pagination(c core.IHTTPContext) error {
	organizationSvc := services.NewOrganizationService(c)

	// Check if filtering by ministry or department
	ministryID := c.QueryParam("ministry_id")
	departmentID := c.QueryParam("department_id")

	if ministryID != "" {
		res, ierr := organizationSvc.FindByMinistry(ministryID, c.GetPageOptions())
		if ierr != nil {
			return c.JSON(ierr.GetStatus(), ierr.JSON())
		}
		return c.JSON(http.StatusOK, res)
	}

	if departmentID != "" {
		res, ierr := organizationSvc.FindByDepartment(departmentID, c.GetPageOptions())
		if ierr != nil {
			return c.JSON(ierr.GetStatus(), ierr.JSON())
		}
		return c.JSON(http.StatusOK, res)
	}

	res, ierr := organizationSvc.Pagination(c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m OrganizationController) Find(c core.IHTTPContext) error {
	organizationSvc := services.NewOrganizationService(c)
	organization, err := organizationSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, organization)
}

func (m OrganizationController) Create(c core.IHTTPContext) error {
	input := &requests.OrganizationCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	organizationSvc := services.NewOrganizationService(c)
	payload := &services.OrganizationCreatePayload{}
	_ = utils.Copy(payload, input)
	organization, err := organizationSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, organization)
}

func (m OrganizationController) Update(c core.IHTTPContext) error {
	input := &requests.OrganizationUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	organizationSvc := services.NewOrganizationService(c)
	payload := &services.OrganizationUpdatePayload{}
	_ = utils.Copy(payload, input)
	organization, err := organizationSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, organization)
}

func (m OrganizationController) Delete(c core.IHTTPContext) error {
	organizationSvc := services.NewOrganizationService(c)
	err := organizationSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
