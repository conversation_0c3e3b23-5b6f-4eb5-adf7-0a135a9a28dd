package projectusage

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/consts"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type ProjectUsageController struct{}

func (m ProjectUsageController) GetUsageJob(c core.ICronjobContext) error {
	c.Log().Info("CreateJob AgentBilling is process...")
	agbSvc := services.NewProjectUsageService(c)
	ierr := agbSvc.GetUsage()
	if ierr != nil {
		c.Log().Error(ierr, ierr)
		return nil
	}

	return nil
}

func (m ProjectUsageController) GetUsage(c core.IHTTPContext) error {
	// agbSvc := services.NewProjectUsageService(c)

	// env
	hwBillingURL := c.ENV().String(consts.EnvHuaweiCloudBillingURL)
	hwTokenURL := c.ENV().String(consts.EnvHuaweiCloudTokenURL)
	hwEN3AccessKey := c.ENV().String(consts.EnvHuaweiEN3AccessKey)
	hwEN3SecretKey := c.ENV().String(consts.EnvHuaweiEN3SecretKey)
	hwBBVAccessKey := c.ENV().String(consts.EnvHuaweiBBVAccessKey)
	hwBBVSecretKey := c.ENV().String(consts.EnvHuaweiBBVSecretKey)

	// ierr := agbSvc.GetUsage()
	// if ierr != nil {
	// 	return c.JSON(ierr.GetStatus(), ierr.JSON())
	// }

	return c.JSON(http.StatusOK, map[string]interface{}{
		"hwBillingURL":   hwBillingURL,
		"hwTokenURL":     hwTokenURL,
		"hwEN3AccessKey": hwEN3AccessKey,
		"hwEN3SecretKey": hwEN3SecretKey,
		"hwBBVAccessKey": hwBBVAccessKey,
		"hwBBVSecretKey": hwBBVSecretKey,
	})
}
