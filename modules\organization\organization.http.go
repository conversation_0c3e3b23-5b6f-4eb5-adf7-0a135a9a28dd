package organization

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/csp/csp-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewOrganizationHTTP(e *echo.Echo) {
	organization := &OrganizationController{}

	// Organization routes - all require authentication
	e.GET("/organizations", core.WithHTTPContext(organization.Pagination), middleware.AuthMiddleware())
	e.GET("/organizations/:id", core.WithHTTPContext(organization.Find), middleware.AuthMiddleware())
	e.POST("/organizations", core.WithHTTPContext(organization.Create), middleware.AuthMiddleware())
	e.PUT("/organizations/:id", core.WithHTTPContext(organization.Update), middleware.AuthMiddleware())
	e.DELETE("/organizations/:id", core.WithHTTPContext(organization.Delete), middleware.AuthMiddleware())
}
