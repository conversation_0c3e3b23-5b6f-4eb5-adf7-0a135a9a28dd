-- AlterTable
ALTER TABLE "public"."projects" ADD COLUMN     "organizationsId" UUID;

-- CreateTable
CREATE TABLE "public"."organizations" (
    "id" UUID NOT NULL,
    "name_th" TEXT NOT NULL,
    "name_en" TEXT,
    "short_name_th" TEXT,
    "short_name_en" TEXT,
    "code" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "organizations_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "organizations_name_th_key" ON "public"."organizations"("name_th");

-- CreateIndex
CREATE UNIQUE INDEX "organizations_name_en_key" ON "public"."organizations"("name_en");

-- CreateIndex
CREATE UNIQUE INDEX "organizations_short_name_th_key" ON "public"."organizations"("short_name_th");

-- CreateIndex
CREATE UNIQUE INDEX "organizations_short_name_en_key" ON "public"."organizations"("short_name_en");

-- CreateIndex
CREATE UNIQUE INDEX "organizations_code_key" ON "public"."organizations"("code");

-- CreateIndex
CREATE INDEX "organizations_name_th_idx" ON "public"."organizations"("name_th");

-- AddForeignKey
ALTER TABLE "public"."projects" ADD CONSTRAINT "projects_organizationsId_fkey" FOREIGN KEY ("organizationsId") REFERENCES "public"."organizations"("id") ON DELETE SET NULL ON UPDATE CASCADE;
