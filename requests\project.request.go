package requests

import (
	"strings"

	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
)

type ProjectCreate struct {
	core.BaseValidator
	Name            *string  `json:"name"`
	Code            *string  `json:"code"`
	ContactName     *string  `json:"contact_name"`
	ContactPhone    *string  `json:"contact_phone"`
	ContactEmail    *string  `json:"contact_email"`
	Budget          *float64 `json:"budget"`
	OrganizationID  *string  `json:"organization_id"`
	AccountName     *string  `json:"account_name"`
	RootAccountName *string  `json:"root_account_name"`
	AccountHolder   *string  `json:"account_holder"`
	PsaEmail        *string  `json:"psa_email"`
	ProviderType    *string  `json:"provider_type"`
	Remark          *string  `json:"remark"`
}

func (r *ProjectCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>equired(r.Name, "name"))
	r.Must(r.<PERSON>quired(r.<PERSON>, "root_account_name"))
	r.Must(r.<PERSON>equired(r.AccountHolder, "account_holder"))

	// Validate email format if provided
	r.Must(r.IsEmail(r.ContactEmail, "contact_email"))

	r.Must(r.IsStrRequired(r.Code, "code"))
	r.Must(r.IsStrMin(r.Code, 5, "code"))
	r.Must(r.IsStrUnique(ctx, r.Code, models.Project{}.TableName(), "code", "", "code"))

	r.Must(r.IsStrRequired(r.ProviderType, "provider_type"))
	r.Must(r.IsStrIn(
		r.ProviderType,
		strings.Join([]string{
			string(models.ProjectProviderTypeHWC),
			string(models.ProjectProviderTypeAWS),
			string(models.ProjectProviderTypeCHM)}, "|"),
		"provider_type"))

	r.Must(r.IsEmail(r.PsaEmail, "psa_email"))

	r.Must(r.IsExists(ctx, r.OrganizationID, models.Organization{}.TableName(), "id", "organization_id"))

	return r.Error()
}

type ProjectUpdate struct {
	core.BaseValidator
	Name            *string  `json:"name"`
	Code            *string  `json:"code"`
	ContactName     *string  `json:"contact_name"`
	ContactPhone    *string  `json:"contact_phone"`
	ContactEmail    *string  `json:"contact_email"`
	Budget          *float64 `json:"budget"`
	OrganizationID  *string  `json:"organization_id"`
	AccountName     *string  `json:"account_name"`
	RootAccountName *string  `json:"root_account_name"`
	AccountHolder   *string  `json:"account_holder"`
	PsaEmail        *string  `json:"psa_email"`
	Status          *string  `json:"status"`
	Remark          *string  `json:"remark"`
}

func (r *ProjectUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldCode := ""

	project, _ := repo.Project(cc).FindOne("id = ?", cc.Param("id"))
	if project != nil {
		oldCode = project.Code
	}

	r.Must(r.IsEmail(r.ContactEmail, "contact_email"))
	r.Must(r.IsStrRequired(r.Status, "status"))
	r.Must(r.IsStrMin(r.Code, 5, "code"))
	r.Must(r.IsStrUnique(ctx, r.Code, models.Project{}.TableName(), "code", oldCode, "code"))
	r.Must(r.IsStrIn(
		r.Status,
		strings.Join([]string{
			string(models.ProjectStatusDraft),
			string(models.ProjectStatusActive),
			string(models.ProjectStatusClosed)}, "|"),
		"status"))

	r.Must(r.IsEmail(r.PsaEmail, "psa_email"))

	if r.OrganizationID != nil {
		r.Must(r.IsExists(ctx, r.OrganizationID, models.Organization{}.TableName(), "id", "organization_id"))
	}

	return r.Error()
}
