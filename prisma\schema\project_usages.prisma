model project_usages {
  id              String    @id @default(uuid()) @db.Uuid
  project_id      String    @db.Uuid
  amount          Float
  official_amount Float
  hour_count      Int?
  created_at      DateTime  @default(now())
  updated_at      DateTime  @default(now())
  timestamp       DateTime?
  cycle_id        String    @db.Uuid
  cycle           cycles    @relation(fields: [cycle_id], references: [id], onDelete: Cascade)
  project         projects  @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@index([project_id])
  @@index([cycle_id])
}
