package requests

import (
	core "gitlab.finema.co/finema/idin-core"
)

type ProjectItemCreate struct {
	core.BaseValidator
	Name        *string `json:"name"`
	Description *string `json:"description"`
}

func (r *ProjectItemCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.Is<PERSON>tr<PERSON>equired(r.Name, "name"))
	r.Must(r.<PERSON>tr<PERSON>equired(r.Description, "description"))

	return r.Error()
}

type ProjectItemUpdate struct {
	core.BaseValidator
	Name        *string `json:"name"`
	Description *string `json:"description"`
}

func (r *ProjectItemUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Name, "name"))
	r.Must(r.IsStrRequired(r.Description, "description"))

	return r.Error()
}
