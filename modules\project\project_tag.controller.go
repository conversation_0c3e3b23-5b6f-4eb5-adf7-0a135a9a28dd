package project

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/requests"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ProjectTagController struct {
}

func (m ProjectTagController) Pagination(c core.IHTTPContext) error {
	projectTagSvc := services.NewProjectTagService(c)
	res, ierr := projectTagSvc.Pagination(c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m ProjectTagController) Find(c core.IHTTPContext) error {
	projectTagSvc := services.NewProjectTagService(c)
	projectTag, err := projectTagSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.<PERSON><PERSON><PERSON>())
	}

	return c.JSO<PERSON>(http.StatusOK, projectTag)
}

func (m ProjectTagController) Create(c core.IHTTPContext) error {
	input := &requests.ProjectTagCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	projectTagSvc := services.NewProjectTagService(c)
	payload := &services.ProjectTagCreatePayload{}
	_ = utils.Copy(payload, input)
	projectTag, err := projectTagSvc.Create(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, projectTag)
}

func (m ProjectTagController) Update(c core.IHTTPContext) error {
	input := &requests.ProjectTagUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	projectTagSvc := services.NewProjectTagService(c)
	payload := &services.ProjectTagUpdatePayload{}
	_ = utils.Copy(payload, input)
	projectTag, err := projectTagSvc.Update(c.Param("tag_id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, projectTag)
}

func (m ProjectTagController) Delete(c core.IHTTPContext) error {
	projectTagSvc := services.NewProjectTagService(c)
	err := projectTagSvc.Delete(c.Param("tag_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
