# Project Tag Filtering

## Overview

The project API now supports filtering projects by tags. This feature allows you to retrieve projects that have specific tags assigned to them.

## API Endpoint

```
GET /projects?tags=tag1,tag2,tag3
```

## Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `tags` | string | Comma-separated list of tag names to filter by | `production,critical` |

## Behavior

### AND Operation
The tag filtering uses an **AND** operation, meaning projects must have **ALL** specified tags to be included in the results.

**Example:**
- Request: `GET /projects?tags=production,critical`
- Result: Only projects that have BOTH "production" AND "critical" tags

### Case Sensitivity
Tag filtering is **case-sensitive**. "Production" and "production" are treated as different tags.

### Whitespace Handling
Leading and trailing whitespace is automatically trimmed from tag names.

**Example:**
- Request: `GET /projects?tags= production , critical `
- Processed as: `production,critical`

## Usage Examples

### Single Tag Filter
```bash
curl -X GET "http://localhost:3001/projects?tags=production" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Multiple Tags Filter
```bash
curl -X GET "http://localhost:3001/projects?tags=production,critical,aws" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Combined with Other Filters
```bash
curl -X GET "http://localhost:3001/projects?tags=production&status=ACTIVE&provider_type=AWS" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### With Search and Sorting
```bash
curl -X GET "http://localhost:3001/projects?tags=production&q=test&sort_by=code_desc" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Response Format

The response includes the standard project pagination format with project_tags included:

```json
{
  "data": [
    {
      "id": "project-id",
      "name": "Project Name",
      "code": "PROJ001",
      "status": "ACTIVE",
      "project_tags": [
        {
          "id": "tag-id-1",
          "name": "production",
          "color": "#ff0000"
        },
        {
          "id": "tag-id-2", 
          "name": "critical",
          "color": "#00ff00"
        }
      ],
      // ... other project fields
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 5,
    "total_pages": 1
  }
}
```

## Implementation Details

### Database Query
The tag filtering is implemented using a subquery that ensures projects have all specified tags:

```sql
SELECT * FROM projects 
WHERE id IN (
  SELECT project_id 
  FROM project_tags 
  WHERE name IN ('tag1', 'tag2') 
  GROUP BY project_id 
  HAVING COUNT(DISTINCT name) = 2
)
```

### Performance Considerations
- The query uses an index on `project_tags.project_id` for optimal performance
- For better performance with many tags, consider limiting the number of tags in a single request
- The query is optimized to work efficiently with the existing project filtering system

## Error Handling

### Empty Tags Parameter
If the `tags` parameter is empty or contains only whitespace, no tag filtering is applied:

```bash
# These requests are equivalent to not specifying tags at all
GET /projects?tags=
GET /projects?tags=   
```

### Non-existent Tags
If you filter by tags that don't exist, the API will return an empty result set:

```bash
GET /projects?tags=nonexistent-tag
# Returns: {"data": [], "meta": {"total": 0, ...}}
```

### Invalid Characters
Tag names are used as-is in the query. Special characters should be URL-encoded:

```bash
# For tags with special characters like "test-env"
GET /projects?tags=test%2Denv
```

## Compatibility

This feature is fully compatible with all existing project filtering options:

- ✅ Status filtering (`status=ACTIVE`)
- ✅ Provider type filtering (`provider_type=AWS`)
- ✅ Organization filtering (`organization_id=...`)
- ✅ Account holder filtering (`account_holder=...`)
- ✅ Created by filtering (`created_by_id=...`)
- ✅ Search functionality (`q=search-term`)
- ✅ Sorting options (`sort_by=code_desc`)
- ✅ Pagination (`page=1&limit=10`)

## Migration Notes

This is a **non-breaking change**. Existing API calls will continue to work exactly as before. The `tags` parameter is optional and only applies filtering when specified.

## Testing

See `tests/project_tag_filter_test.md` for comprehensive test cases and examples.
