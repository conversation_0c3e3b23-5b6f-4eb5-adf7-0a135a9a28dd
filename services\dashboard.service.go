package services

import (
	"net/http"
	"strings"

	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
)

type IDashboardService interface {
	GetDashboardData() (*DashboardResponse, core.IError)
	GetProviderTypeStats(status ...models.ProjectStatus) ([]ProviderTypeStat, core.IError)
}

type DashboardResponse struct {
	ProjectStats      *ProjectStats      `json:"project_stats"`
	LatestProjects    []models.Project   `json:"latest_projects"`
	ProviderTypeStats []ProviderTypeStat `json:"provider_type_stats"`
}

type ProjectStats struct {
	Total  int64 `json:"total"`
	Draft  int64 `json:"draft"`
	Active int64 `json:"active"`
	Closed int64 `json:"closed"`
}

type ProviderTypeStat struct {
	ProviderType models.ProjectProviderType `json:"provider_type"`
	Count        int64                      `json:"count"`
	TotalUsage   float64                    `json:"total_usage"`
	TotalBudget  float64                    `json:"total_budget"`
	Percent      float64                    `json:"percent"`
}

type dashboardService struct {
	ctx core.IContext
}

func (s dashboardService) GetDashboardData() (*DashboardResponse, core.IError) {
	// Get project statistics by status
	projectStats, err := s.getProjectStats()
	if err != nil {
		return nil, err
	}

	// Get 5 latest projects
	latestProjects, err := s.getLatestProjects()
	if err != nil {
		return nil, err
	}

	providerStats, err := s.getProviderTypeStats(models.ProjectStatusActive, models.ProjectStatusDraft)
	if err != nil {
		return nil, err
	}

	return &DashboardResponse{
		ProjectStats:      projectStats,
		LatestProjects:    latestProjects,
		ProviderTypeStats: providerStats,
	}, nil
}

func (s dashboardService) getProjectStats() (*ProjectStats, core.IError) {
	var stats ProjectStats

	// Get total count
	count, err := repo.Project(s.ctx).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get total project count",
		}, err)
	}
	stats.Total = count

	// Get count by status
	draftCount, err := repo.Project(s.ctx).Where("status = ?", models.ProjectStatusDraft).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get draft project count",
		}, err)
	}
	stats.Draft = draftCount

	activeCount, err := repo.Project(s.ctx).Where("status = ?", models.ProjectStatusActive).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get active project count",
		}, err)
	}
	stats.Active = activeCount

	closedCount, err := repo.Project(s.ctx).Where("status = ?", models.ProjectStatusClosed).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get closed project count",
		}, err)
	}
	stats.Closed = closedCount

	return &stats, nil
}

func (s dashboardService) getLatestProjects() ([]models.Project, core.IError) {
	var projects []models.Project

	projects, err := repo.Project(s.ctx, repo.ProjectWithAllRelations(), repo.ProjectCodeBy(&core.PageOptions{})).
		Limit(5).
		FindAll()

	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "FIND_FAILED",
			Message: "Failed to get latest projects",
		}, err)
	}

	return projects, nil
}

func (s dashboardService) getProviderTypeStats(status ...models.ProjectStatus) ([]ProviderTypeStat, core.IError) {
	var stats []ProviderTypeStat

	// Get all provider types
	providerTypes := []models.ProjectProviderType{
		models.ProjectProviderTypeHWC,
		models.ProjectProviderTypeAWS,
		models.ProjectProviderTypeCHM,
	}

	for _, providerType := range providerTypes {
		var totalUsage float64
		var totalBudget float64

		// Build where conditions based on status parameter
		var countQuery, usageQuery, budgetQuery string
		var countArgs, usageArgs, budgetArgs []interface{}

		// Filter out empty status values
		validStatuses := make([]models.ProjectStatus, 0)
		for _, s := range status {
			if s != "" {
				validStatuses = append(validStatuses, s)
			}
		}

		if len(validStatuses) > 0 {
			// Filter by multiple statuses using IN clause
			placeholders := make([]string, len(validStatuses))
			statusArgs := make([]interface{}, len(validStatuses))
			for i, s := range validStatuses {
				placeholders[i] = "?"
				statusArgs[i] = s
			}
			statusInClause := "(" + strings.Join(placeholders, ",") + ")"

			countQuery = "provider_type = ? AND status IN " + statusInClause
			countArgs = append([]interface{}{providerType}, statusArgs...)

			usageQuery = "projects.provider_type = ? AND projects.status IN " + statusInClause + " AND projects.deleted_at IS NULL"
			usageArgs = append([]interface{}{providerType}, statusArgs...)

			budgetQuery = "provider_type = ? AND status IN " + statusInClause + " AND deleted_at IS NULL"
			budgetArgs = append([]interface{}{providerType}, statusArgs...)
		} else {
			// Get all statuses
			countQuery = "provider_type = ?"
			countArgs = []interface{}{providerType}

			usageQuery = "projects.provider_type = ? AND projects.deleted_at IS NULL"
			usageArgs = []interface{}{providerType}

			budgetQuery = "provider_type = ? AND deleted_at IS NULL"
			budgetArgs = []interface{}{providerType}
		}

		// Get project count by provider type
		count, err := repo.Project(s.ctx).Where(countQuery, countArgs...).Count()
		if err != nil {
			return nil, s.ctx.NewError(core.Error{
				Status:  http.StatusInternalServerError,
				Code:    "COUNT_FAILED",
				Message: "Failed to get provider type count",
			}, err)
		}

		// Get total usage for this provider type
		// subquery หา project_usages ล่าสุดของแต่ละ project
		latestUsageSub := s.ctx.DB().
			Table("project_usages pu1").
			Select("pu1.project_id, pu1.amount").
			Where("pu1.timestamp = (SELECT MAX(pu2.timestamp) FROM project_usages pu2 WHERE pu2.project_id = pu1.project_id)")
		// Join with project_usages to get the sum of amounts
		dbErr := s.ctx.DB().Table("projects").
			Select("COALESCE(SUM(latest.amount), 0) as total_usage").
			Joins("LEFT JOIN (?) AS latest ON projects.id = latest.project_id", latestUsageSub).
			Where(usageQuery, usageArgs...).
			Scan(&totalUsage).Error

		if dbErr != nil {
			return nil, s.ctx.NewError(core.Error{
				Status:  http.StatusInternalServerError,
				Code:    "DB_QUERY_FAILED",
				Message: "Failed to get provider type usage",
			}, core.Error{})
		}

		// Get total budget for this provider type
		dbErr = s.ctx.DB().Table("projects").
			Select("COALESCE(SUM(budget), 0) as total_budget").
			Where(budgetQuery, budgetArgs...).
			Scan(&totalBudget).Error

		if dbErr != nil {
			return nil, s.ctx.NewError(core.Error{
				Status:  http.StatusInternalServerError,
				Code:    "DB_QUERY_FAILED",
				Message: "Failed to get provider type budget",
			}, core.Error{})
		}

		// Calculate percentage (usage / budget * 100)
		var percent float64
		if totalBudget > 0 {
			percent = (totalUsage / totalBudget) * 100
		}

		stats = append(stats, ProviderTypeStat{
			ProviderType: providerType,
			Count:        count,
			TotalUsage:   totalUsage,
			TotalBudget:  totalBudget,
			Percent:      percent,
		})
	}

	return stats, nil
}

func (s dashboardService) GetProviderTypeStats(status ...models.ProjectStatus) ([]ProviderTypeStat, core.IError) {
	return s.getProviderTypeStats(status...)
}

func NewDashboardService(ctx core.IContext) IDashboardService {
	return &dashboardService{ctx: ctx}
}
