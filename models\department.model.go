package models

type Department struct {
	BaseModel
	MinistryID  string  `json:"ministry_id" gorm:"column:ministry_id"`
	NameTh      string  `json:"name_th" gorm:"column:name_th"`
	NameEn      *string `json:"name_en" gorm:"column:name_en"`
	ShortNameTh *string `json:"short_name_th" gorm:"column:short_name_th"`
	ShortNameEn *string `json:"short_name_en" gorm:"column:short_name_en"`
	Code        *int    `json:"code" gorm:"column:code"`

	// Relations
	Ministry *Ministry `json:"ministry,omitempty" gorm:"foreignKey:MinistryID"`
}

func (Department) TableName() string {
	return "departments"
}
