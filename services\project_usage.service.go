package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"gitlab.finema.co/finema/csp/csp-api/consts"
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IProjectUsageService interface {
	GetUsage() core.IError
	GetAccountID(accountName, rootAccountName string) (string, core.IError)
}

type projectUsageService struct {
	ctx core.IContext
}

type BillingResponse struct {
	Currency   string `json:"currency"`
	TotalCount int    `json:"total_count"`
	CostData   []struct {
		Dimensions []struct {
			Key   string `json:"key"`
			Value string `json:"value"`
		} `json:"dimensions"`
		Costs []struct {
			TimeDimensionValue string `json:"time_dimension_value"`
			TimeMeasureID      int    `json:"time_measure_id"`
			Amount             string `json:"amount"`
			OfficialAmount     string `json:"official_amount"`
		} `json:"costs"`
		AmountByCosts         string `json:"amount_by_costs"`
		OfficialAmountByCosts string `json:"official_amount_by_costs"`
	} `json:"cost_data"`
}

func NewProjectUsageService(ctx core.IContext) IProjectUsageService {
	return &projectUsageService{ctx: ctx}
}

// Cronjob Get Usage
func (s projectUsageService) GetUsage() core.IError {

	// Get Current DateTime UTC+7
	currentDateTime := time.Now().Add(7 * time.Hour)

	// Get Billing Cycle
	currentBillingCycle, ierr := repo.Cycle(s.ctx, repo.CycleCurrent()).FindOne()
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	if currentBillingCycle.CycleEndDate.Before(currentDateTime) {
		// Update Next Billing Cycle to current
		ierr = repo.Cycle(s.ctx).Where("cycle_no = ?", currentBillingCycle.CycleNo+1).Updates(map[string]interface{}{
			"status": "current",
		})
		if ierr != nil {
			return s.ctx.NewError(ierr, ierr)
		}

		// update current billing cycle to unbilled
		ierr = repo.Cycle(s.ctx).Where("id = ?", currentBillingCycle.ID).Updates(map[string]interface{}{
			"status": "unbilled",
		})
		if ierr != nil {
			return s.ctx.NewError(ierr, ierr)
		}
	}

	billingCycle, ierr := repo.Cycle(s.ctx, repo.CycleCurrent()).FindOne()
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	// Get Projects
	projects, ierr := repo.Project(s.ctx).Where("status = ?", "ACTIVE").FindAll()
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	// utils.LogStruct(projects)

	// Group projects by RootAccountName
	projectsByRootAccount := make(map[string][]models.Project)
	for _, project := range projects {
		projectsByRootAccount[project.RootAccountName] = append(projectsByRootAccount[project.RootAccountName], project)
	}

	// Process each root account separately
	for rootAccountName, rootProjects := range projectsByRootAccount {
		// Get Usage for this root account
		billingData, err := s.getBillingFromHuaweiCloud(billingCycle.CycleStartDate.Format("2006-01-02"), billingCycle.CycleEndDate.Format("2006-01-02"), rootAccountName)
		if err != nil {
			return s.ctx.NewError(err, core.Error{
				Status:  http.StatusInternalServerError,
				Code:    "GET_BILLING_FAILED",
				Message: fmt.Sprintf("Failed to get billing from huawei cloud for root account %s", rootAccountName),
			})
		}

		// Find Billing By billingData.costData.dimensions[0].value is projects is AccountID
		for _, project := range rootProjects {
			found := false
			var amount, officialAmount float64

			// Check if project has billing data
			for _, costData := range billingData.CostData {
				if costData.Dimensions[0].Value == project.AccountID {
					found = true
					fmt.Println(costData.AmountByCosts, costData.OfficialAmountByCosts, "cost")

					// Convert amount to float64
					amount, _ = strconv.ParseFloat(costData.AmountByCosts, 64)
					officialAmount, _ = strconv.ParseFloat(costData.OfficialAmountByCosts, 64)
					break
				}
			}

			// If no billing data found, set amounts to 0
			if !found {
				amount = 0.0
				officialAmount = 0.0
				fmt.Printf("No billing data found for project AccountID: %s, setting amounts to 0\n", project.AccountID)
			}

			//find project_usage by project_id and cycle_id
			hourCountPusage := 0
			projectUsageTotal, ierr := repo.ProjectUsage(s.ctx).
				Where("project_id = ? AND cycle_id = ?", project.ID, billingCycle.ID).
				Order("timestamp DESC").
				FindAll()
			if ierr != nil {
				return s.ctx.NewError(ierr, ierr)
			}
			if len(projectUsageTotal) > 0 {
				hourCountPusage = projectUsageTotal[0].HourCount + 1
			}

			projectUsage := models.ProjectUsage{
				BaseModelHardDelete: models.NewBaseModelHardDelete(),
				ProjectID:           project.ID,
				Amount:              amount,
				OfficialAmount:      officialAmount,
				HourCount:           hourCountPusage,
				CycleID:             billingCycle.ID,
				TimeStamp:           utils.GetCurrentDateTime(),
			}
			ierr = repo.ProjectUsage(s.ctx).Create(&projectUsage)
			if ierr != nil {
				return s.ctx.NewError(ierr, ierr)
			}
		}
	}

	// find project usage by cycle id and update total cost
	projectUsages, ierr := repo.ProjectUsage(s.ctx).Where("cycle_id = ?", billingCycle.ID).FindAll()
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	totalCost := 0.0
	officialTotalCost := 0.0

	for _, projectUsage := range projectUsages {
		totalCost += projectUsage.Amount
		officialTotalCost += projectUsage.OfficialAmount
	}

	ierr = repo.Cycle(s.ctx).Where("id = ?", billingCycle.ID).Updates(map[string]interface{}{
		"total_cost":          totalCost,
		"total_official_cost": officialTotalCost,
		"hour_count":          billingCycle.HourCount + 1,
		"updated_at":          utils.GetCurrentDateTime(),
	})
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return nil

}

func (s projectUsageService) getBillingFromHuaweiCloud(startDate, endDate, rootAccountName string) (*BillingResponse, error) {
	token, err := s.getTokenFromAKSK(rootAccountName)
	if err != nil {
		return nil, err
	}

	type billingRequestBody struct {
		TimeCondition struct {
			TimeMeasureID int    `json:"time_measure_id"`
			BeginTime     string `json:"begin_time"`
			EndTime       string `json:"end_time"`
		} `json:"time_condition"`
		Groupby []struct {
			Type string `json:"type"`
			Key  string `json:"key"`
		} `json:"groupby"`
		CostType   string `json:"cost_type"`
		AmountType string `json:"amount_type"`
		Limit      int    `json:"limit"`
	}

	requestPayload := billingRequestBody{
		TimeCondition: struct {
			TimeMeasureID int    `json:"time_measure_id"`
			BeginTime     string `json:"begin_time"`
			EndTime       string `json:"end_time"`
		}{
			TimeMeasureID: 1,
			BeginTime:     startDate,
			EndTime:       endDate,
		},
		Groupby: []struct {
			Type string `json:"type"`
			Key  string `json:"key"`
		}{
			{
				Type: "dimension",
				Key:  "ASSOCIATED_ACCOUNT",
			},
		},
		CostType:   "ORIGINAL_COST",
		AmountType: "PAYMENT_AMOUNT",
		Limit:      100,
	}

	jsonPayload, err := json.Marshal(requestPayload)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", s.ctx.ENV().String(consts.EnvHuaweiCloudBillingURL)+"/v4/costs/cost-analysed-bills/query", bytes.NewBuffer(jsonPayload))
	// req, err := http.NewRequest("POST", "https://bss-intl.myhuaweicloud.com/v4/costs/cost-analysed-bills/query", bytes.NewBuffer(jsonPayload))

	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Auth-Token", token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to get billing data: %s", string(body))
	}

	var billingData BillingResponse
	if err := json.NewDecoder(resp.Body).Decode(&billingData); err != nil {
		return nil, err
	}

	return &billingData, nil
}

func (s projectUsageService) getTokenFromAKSK(rootAccountName string) (string, error) {
	type requestBody struct {
		Auth struct {
			Identity struct {
				Methods []string `json:"methods"`
				HwAkSk  struct {
					Access struct {
						Key string `json:"key"`
					} `json:"access"`
					Secret struct {
						Key string `json:"key"`
					} `json:"secret"`
				} `json:"hw_ak_sk"`
			} `json:"identity"`
			Scope struct {
				Project struct {
					Name string `json:"name"`
				} `json:"project"`
			} `json:"scope"`
		} `json:"auth"`
	}

	payload := requestBody{}
	payload.Auth.Identity.Methods = []string{"hw_ak_sk"}

	// Select AK/SK based on RootAccountName
	switch rootAccountName {
	case "en3":
		payload.Auth.Identity.HwAkSk.Access.Key = s.ctx.ENV().String(consts.EnvHuaweiEN3AccessKey)
		payload.Auth.Identity.HwAkSk.Secret.Key = s.ctx.ENV().String(consts.EnvHuaweiEN3SecretKey)
	case "bbv":
		payload.Auth.Identity.HwAkSk.Access.Key = s.ctx.ENV().String(consts.EnvHuaweiBBVAccessKey)
		payload.Auth.Identity.HwAkSk.Secret.Key = s.ctx.ENV().String(consts.EnvHuaweiBBVSecretKey)
	default:
		return "", fmt.Errorf("unsupported root account name: %s", rootAccountName)
	}

	payload.Auth.Scope.Project.Name = "ap-southeast-2"

	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}

	// req, err := http.NewRequest("POST", s.ctx.ENV().String(consts.EnvHuaweiCloudTokenURL)+"/v3/auth/tokens", bytes.NewBuffer(jsonPayload))
	req, err := http.NewRequest("POST", s.ctx.ENV().String(consts.EnvHuaweiCloudTokenURL)+"/v3/auth/tokens", bytes.NewBuffer(jsonPayload))

	if err != nil {
		return "", err
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return "", fmt.Errorf("failed to get token")
	}

	token := resp.Header.Get("X-Subject-Token")
	if token == "" {
		return "", fmt.Errorf("X-Subject-Token not found in response header")
	}

	return token, nil
}

type SubCustomerInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	Status      int    `json:"status"`
	OrgID       string `json:"org_id"`
	OrgName     string `json:"org_name"`
}

type SubCustomerResponse struct {
	TotalCount       int               `json:"total_count"`
	SubCustomerInfos []SubCustomerInfo `json:"sub_customer_infos"`
}

func (s projectUsageService) GetAccountID(accountName, rootAccountName string) (string, core.IError) {
	token, err := s.getTokenFromAKSK(rootAccountName)
	if err != nil {
		return "", s.ctx.NewError(err, core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "GET_TOKEN_FAILED",
			Message: "Failed to get token from huawei cloud",
		})
	}

	// url := fmt.Sprintf("%s/v2/enterprises/multi-accounts/sub-customers?sub_customer_account_name=%s", s.ctx.ENV().String(consts.EnvHuaweiCloudBillingURL), accountName)
	url := fmt.Sprintf("%s/v2/enterprises/multi-accounts/sub-customers?sub_customer_account_name=%s", s.ctx.ENV().String(consts.EnvHuaweiCloudBillingURL), accountName)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", s.ctx.NewError(err, core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "CREATE_REQUEST_FAILED",
			Message: "Failed to create request",
		})
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Auth-Token", token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", s.ctx.NewError(err, core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "REQUEST_FAILED",
			Message: "Failed to get account id from huawei cloud",
		})
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", s.ctx.NewError(fmt.Errorf("failed to get account id: %s", string(body)), core.Error{
			Status:  resp.StatusCode,
			Code:    "GET_ACCOUNT_ID_FAILED",
			Message: "Failed to get account id from huawei cloud",
		})
	}

	var subCustomerResponse SubCustomerResponse
	if err := json.NewDecoder(resp.Body).Decode(&subCustomerResponse); err != nil {
		return "", s.ctx.NewError(err, core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "DECODE_RESPONSE_FAILED",
			Message: "Failed to decode response from huawei cloud",
		})
	}

	if subCustomerResponse.TotalCount == 0 || len(subCustomerResponse.SubCustomerInfos) == 0 {
		return "", s.ctx.NewError(fmt.Errorf("account not found"), core.Error{
			Status:  http.StatusNotFound,
			Code:    "ACCOUNT_NOT_FOUND",
			Message: "Account not found",
		})
	}

	return subCustomerResponse.SubCustomerInfos[0].ID, nil
}
