### Variables
@base_url = http://localhost:3001
@auth_token = your-auth-token-here
@project_id = project-uuid-here

### Login (to get auth token)
POST {{base_url}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### Get All Cycles for a Project
GET {{base_url}}/projects/{{project_id}}/cycles
Authorization: Bearer {{auth_token}}

### Get All Cycles for a Project with Pagination
GET {{base_url}}/projects/{{project_id}}/cycles?page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get All Cycles for a Project with Search
GET {{base_url}}/projects/{{project_id}}/cycles?q=2024
Authorization: Bearer {{auth_token}}

### Get All Cycles for a Project with Pagination and Search
GET {{base_url}}/projects/{{project_id}}/cycles?page=1&limit=5&q=current
Authorization: Bearer {{auth_token}}

### Get All Cycles for a Project with Custom Ordering
GET {{base_url}}/projects/{{project_id}}/cycles?order_by=cycle_start_date ASC
Authorization: Bearer {{auth_token}}

### Test with Invalid Project ID
GET {{base_url}}/projects/invalid-uuid/cycles
Authorization: Bearer {{auth_token}}

### Test with Non-existent Project ID
GET {{base_url}}/projects/00000000-0000-0000-0000-000000000000/cycles
Authorization: Bearer {{auth_token}}
