/*
  Warnings:

  - You are about to drop the column `division_id` on the `projects` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."projects" DROP CONSTRAINT "projects_division_id_fkey";

-- DropIndex
DROP INDEX "public"."projects_name_code_status_division_id_idx";

-- AlterTable
ALTER TABLE "public"."projects" DROP COLUMN "division_id";

-- CreateIndex
CREATE INDEX "projects_name_code_status_organization_id_idx" ON "public"."projects"("name", "code", "status", "organization_id");
