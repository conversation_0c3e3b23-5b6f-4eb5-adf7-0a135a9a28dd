package models

type ProjectItem struct {
	BaseModelHardDelete
	Name        string `json:"name" gorm:"column:name"`
	Description string `json:"description" gorm:"column:description"`
	ProjectID   string `json:"project_id" gorm:"column:project_id"`
	// Relations
	Project *Project `json:"project,omitempty" gorm:"foreignKey:ProjectID"`
}

func (ProjectItem) TableName() string {
	return "project_items"
}
