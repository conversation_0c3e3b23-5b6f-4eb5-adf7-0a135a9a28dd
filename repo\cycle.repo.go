package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type CycleOption func(repository.IRepository[models.Cycle])

var Cycle = func(c core.IContext, options ...CycleOption) repository.IRepository[models.Cycle] {
	r := repository.New[models.Cycle](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func CycleCurrent() CycleOption {
	return func(c repository.IRepository[models.Cycle]) {
		c.Where("status = ?", "current")
	}
}

func CycleOrderBy(pageOptions *core.PageOptions) CycleOption {
	return func(c repository.IRepository[models.Cycle]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("cycle_start_date DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func CycleByDateRange(startDate, endDate string) CycleOption {
	return func(c repository.IRepository[models.Cycle]) {
		if startDate != "" && endDate != "" {
			c.Where("cycle_start_date >= ? AND cycle_end_date <= ?", startDate, endDate)
		} else if startDate != "" {
			c.Where("cycle_start_date >= ?", startDate)
		} else if endDate != "" {
			c.Where("cycle_end_date <= ?", endDate)
		}
	}
}

func CycleBySearch(search string) CycleOption {
	if search == "" {
		return func(c repository.IRepository[models.Cycle]) {}
	}
	return func(c repository.IRepository[models.Cycle]) {
		// Search by date range or ID
		c.Where("id::text ILIKE ? OR cycle_start_date::text ILIKE ? OR cycle_end_date::text ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}
}

func CycleByStatus(statuses []string) CycleOption {
	if len(statuses) == 0 {
		return func(c repository.IRepository[models.Cycle]) {}
	}
	return func(c repository.IRepository[models.Cycle]) {
		c.Where("status IN ?", statuses)
	}
}

func CycleWithProjectUsages() CycleOption {
	return func(c repository.IRepository[models.Cycle]) {
		c.Preload("ProjectUsages")
	}
}

func CycleWithCalculatedTotals() CycleOption {
	return func(c repository.IRepository[models.Cycle]) {
		// Use a subquery to calculate totals from ProjectUsage
		c.Select(`cycles.*,
			COALESCE((SELECT SUM(pu.amount) FROM project_usages pu WHERE pu.cycle_id = cycles.id), 0) as total_cost,
			COALESCE((SELECT SUM(pu.official_amount) FROM project_usages pu WHERE pu.cycle_id = cycles.id), 0) as total_official_cost`)
	}
}
