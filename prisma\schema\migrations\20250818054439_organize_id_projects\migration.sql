/*
  Warnings:

  - You are about to drop the column `organizationsId` on the `projects` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."projects" DROP CONSTRAINT "projects_organizationsId_fkey";

-- AlterTable
ALTER TABLE "public"."projects" DROP COLUMN "organizationsId",
ADD COLUMN     "organization_Id" UUID;

-- AddForeignKey
ALTER TABLE "public"."projects" ADD CONSTRAINT "projects_organization_Id_fkey" FOREIGN KEY ("organization_Id") REFERENCES "public"."organizations"("id") ON DELETE SET NULL ON UPDATE CASCADE;
