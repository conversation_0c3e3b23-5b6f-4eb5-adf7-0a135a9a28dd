package project

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/csp/csp-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewProjectHTTP(e *echo.Echo) {
	project := &ProjectController{}
	projectItem := &ProjectItemController{}
	projectTag := &ProjectTagController{}

	// Project routes - all require authentication
	e.GET("/projects", core.WithHTTPContext(project.Pagination), middleware.AuthMiddleware())
	e.GET("/projects/:id", core.WithHTTPContext(project.Find), middleware.AuthMiddleware())
	e.GET("/projects/:id/usages", core.WithHTTPContext(project.GetUsages), middleware.AuthMiddleware())
	e.GET("/projects/:id/usages/cycles/:cycle_id", core.WithHTTPContext(project.GetUsagesByCycle), middleware.AuthMiddleware())
	e.GET("/projects/:id/cycles", core.WithHTTPContext(project.GetCycles), middleware.AuthMiddleware())
	e.POST("/projects", core.WithHTTPContext(project.Create), middleware.AuthMiddleware())
	e.PUT("/projects/:id", core.WithHTTPContext(project.Update), middleware.AuthMiddleware())
	e.DELETE("/projects/:id", core.WithHTTPContext(project.Delete), middleware.AuthMiddleware())

	e.GET("/projects/:id/items", core.WithHTTPContext(projectItem.Find), middleware.AuthMiddleware())
	e.POST("/projects/:id/items", core.WithHTTPContext(projectItem.Create), middleware.AuthMiddleware())
	e.PUT("/projects/:id/items/:item_id", core.WithHTTPContext(projectItem.Update), middleware.AuthMiddleware())
	e.DELETE("/projects/:id/items/:item_id", core.WithHTTPContext(projectItem.Delete), middleware.AuthMiddleware())

	e.GET("/project-tags", core.WithHTTPContext(projectTag.Pagination), middleware.AuthMiddleware())
	e.GET("/projects/:id/tags", core.WithHTTPContext(projectTag.Find), middleware.AuthMiddleware())
	e.POST("/projects/:id/tags", core.WithHTTPContext(projectTag.Create), middleware.AuthMiddleware())
	e.PUT("/projects/:id/tags/:tag_id", core.WithHTTPContext(projectTag.Update), middleware.AuthMiddleware())
	e.DELETE("/projects/:id/tags/:tag_id", core.WithHTTPContext(projectTag.Delete), middleware.AuthMiddleware())
}
