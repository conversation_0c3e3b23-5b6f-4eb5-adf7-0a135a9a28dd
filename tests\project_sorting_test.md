# Project Sorting Implementation Test Plan

## Overview
This document outlines the test cases and procedures to verify the project sorting functionality works correctly.

## Prerequisites
1. Running API server
2. Valid authentication token
3. Test data with projects that have:
   - Different project codes
   - Various budget amounts (including null values)
   - Usage data with different amounts
   - Projects with and without usage data

## Test Cases

### 1. Basic Sorting Tests

#### Test 1.1: Default Sorting (No sort_by parameter)
```bash
curl -X GET "http://localhost:3001/projects?page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects sorted by created_at DESC (newest first)

#### Test 1.2: Project Code Descending
```bash
curl -X GET "http://localhost:3001/projects?sort_by=code_desc&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects sorted by code in descending order (Z-A, 9-0)

#### Test 1.3: Project Code Ascending
```bash
curl -X GET "http://localhost:3001/projects?sort_by=code_asc&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects sorted by code in ascending order (A-Z, 0-9)

#### Test 1.4: Budget Descending
```bash
curl -X GET "http://localhost:3001/projects?sort_by=budget_desc&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects sorted by budget from highest to lowest, null budgets at the end

### 2. Usage-Based Sorting Tests

#### Test 2.1: Usage Amount Descending
```bash
curl -X GET "http://localhost:3001/projects?sort_by=usage_desc&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects sorted by latest usage amount from highest to lowest

#### Test 2.2: Usage Percentage Descending
```bash
curl -X GET "http://localhost:3001/projects?sort_by=usage_percent_desc&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects sorted by usage percentage (usage/budget*100) from highest to lowest

### 3. Error Handling Tests

#### Test 3.1: Invalid Sort Option
```bash
curl -X GET "http://localhost:3001/projects?sort_by=invalid_option&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** 400 Bad Request with error message about invalid sort option

#### Test 3.2: Empty Sort Option
```bash
curl -X GET "http://localhost:3001/projects?sort_by=&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Default sorting behavior (created_at DESC)

### 4. Combined Filtering and Sorting Tests

#### Test 4.1: Status Filter with Code Sorting
```bash
curl -X GET "http://localhost:3001/projects?status=ACTIVE&sort_by=code_desc&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Only ACTIVE projects, sorted by code descending

#### Test 4.2: Provider Type Filter with Budget Sorting
```bash
curl -X GET "http://localhost:3001/projects?provider_type=AWS&sort_by=budget_desc&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Only AWS projects, sorted by budget descending

#### Test 4.3: Multiple Filters with Usage Sorting
```bash
curl -X GET "http://localhost:3001/projects?status=ACTIVE&provider_type=HWC&sort_by=usage_desc&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Only ACTIVE HWC projects, sorted by usage amount descending

### 5. Pagination with Sorting Tests

#### Test 5.1: Multiple Pages with Consistent Sorting
```bash
# Page 1
curl -X GET "http://localhost:3001/projects?sort_by=code_desc&page=1&limit=3" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Page 2
curl -X GET "http://localhost:3001/projects?sort_by=code_desc&page=2&limit=3" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Consistent sorting across pages, no duplicate or missing projects

### 6. Edge Cases Tests

#### Test 6.1: Projects with Null Budget and Usage Percentage Sorting
```bash
curl -X GET "http://localhost:3001/projects?sort_by=usage_percent_desc&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects with null budget should appear at the end with 0% usage

#### Test 6.2: Projects without Usage Data
```bash
curl -X GET "http://localhost:3001/projects?sort_by=usage_desc&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Expected:** Projects without usage data should appear at the end

## Automated Test Script

Here's a bash script to run all the basic tests:

```bash
#!/bin/bash

# Configuration
BASE_URL="http://localhost:3001"
TOKEN="YOUR_AUTH_TOKEN_HERE"

echo "Starting Project Sorting Tests..."

# Test 1: Default sorting
echo "Test 1: Default sorting"
curl -s -X GET "$BASE_URL/projects?page=1&limit=3" \
  -H "Authorization: Bearer $TOKEN" | jq '.data[].code'

# Test 2: Code descending
echo "Test 2: Code descending"
curl -s -X GET "$BASE_URL/projects?sort_by=code_desc&page=1&limit=3" \
  -H "Authorization: Bearer $TOKEN" | jq '.data[].code'

# Test 3: Code ascending
echo "Test 3: Code ascending"
curl -s -X GET "$BASE_URL/projects?sort_by=code_asc&page=1&limit=3" \
  -H "Authorization: Bearer $TOKEN" | jq '.data[].code'

# Test 4: Budget descending
echo "Test 4: Budget descending"
curl -s -X GET "$BASE_URL/projects?sort_by=budget_desc&page=1&limit=3" \
  -H "Authorization: Bearer $TOKEN" | jq '.data[] | {code: .code, budget: .budget}'

# Test 5: Usage descending
echo "Test 5: Usage descending"
curl -s -X GET "$BASE_URL/projects?sort_by=usage_desc&page=1&limit=3" \
  -H "Authorization: Bearer $TOKEN" | jq '.data[] | {code: .code, usage: .project_usage.amount}'

# Test 6: Invalid sort option
echo "Test 6: Invalid sort option"
curl -s -X GET "$BASE_URL/projects?sort_by=invalid&page=1&limit=3" \
  -H "Authorization: Bearer $TOKEN" | jq '.code, .message'

echo "Tests completed!"
```

## Manual Verification Steps

1. **Verify Code Sorting:**
   - Check that code_desc returns projects in reverse alphabetical/numerical order
   - Check that code_asc returns projects in alphabetical/numerical order

2. **Verify Budget Sorting:**
   - Check that budget_desc returns projects with highest budget first
   - Verify that projects with null budget appear at the end

3. **Verify Usage Sorting:**
   - Check that usage_desc returns projects with highest usage amount first
   - Verify that projects without usage data appear at the end

4. **Verify Usage Percentage Sorting:**
   - Check that usage_percent_desc calculates percentage correctly (usage/budget*100)
   - Verify that projects with zero or null budget show 0% and appear at the end

5. **Verify Error Handling:**
   - Confirm that invalid sort options return 400 error with appropriate message
   - Verify that empty sort_by parameter uses default sorting

## Performance Testing

For performance testing with larger datasets:

```bash
# Test with larger page sizes
curl -X GET "http://localhost:3001/projects?sort_by=usage_desc&page=1&limit=100" \
  -H "Authorization: Bearer YOUR_TOKEN" -w "Time: %{time_total}s\n"

# Compare performance between different sort options
for sort in "code_desc" "budget_desc" "usage_desc" "usage_percent_desc"; do
  echo "Testing $sort:"
  curl -X GET "http://localhost:3001/projects?sort_by=$sort&page=1&limit=50" \
    -H "Authorization: Bearer YOUR_TOKEN" -w "Time: %{time_total}s\n" -o /dev/null -s
done
```

## Expected Results Summary

- **Code sorting:** Should work quickly and consistently
- **Budget sorting:** Should handle null values properly
- **Usage sorting:** May be slower due to joins, should handle missing usage data
- **Usage percentage:** Should calculate correctly and handle edge cases
- **Error handling:** Should return appropriate error messages
- **Pagination:** Should maintain sort order across pages
