package services

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type IProjectItemService interface {
	Create(projectId string, input *ProjectItemCreatePayload) (*models.ProjectItem, core.IError)
	Update(id string, input *ProjectItemUpdatePayload) (*models.ProjectItem, core.IError)
	Find(id string) (*models.ProjectItem, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.ProjectItem], core.IError)
	Delete(id string) core.IError
}

type projectItemService struct {
	ctx core.IContext
}

func (s projectItemService) Create(projectId string, input *ProjectItemCreatePayload) (*models.ProjectItem, core.IError) {
	projectSvc := NewProjectService(s.ctx)
	_, ierr := projectSvc.Find(projectId)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}
	projectItem := &models.ProjectItem{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		Name:                input.Name,
		Description:         input.Description,
		ProjectID:           projectId,
	}

	ierr = repo.ProjectItem(s.ctx).Create(projectItem)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(projectItem.ID)
}

func (s projectItemService) Update(id string, input *ProjectItemUpdatePayload) (*models.ProjectItem, core.IError) {
	projectItem, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.Name != "" {
		projectItem.Name = input.Name
	}
	if input.Description != "" {
		projectItem.Description = input.Description
	}

	ierr = repo.ProjectItem(s.ctx).Where("id = ?", id).Updates(projectItem)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(projectItem.ID)
}

func (s projectItemService) Find(id string) (*models.ProjectItem, core.IError) {
	return repo.ProjectItem(s.ctx).FindOne("id = ?", id)
}

func (s projectItemService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.ProjectItem], core.IError) {
	return repo.ProjectItem(s.ctx, repo.ProjectItemOrderBy(pageOptions)).Pagination(pageOptions)
}

func (s projectItemService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.ProjectItem(s.ctx).Delete("id = ?", id)
}

func NewProjectItemService(ctx core.IContext) IProjectItemService {
	return &projectItemService{ctx: ctx}
}
