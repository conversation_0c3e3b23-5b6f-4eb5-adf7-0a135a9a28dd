model cycles {
  id                  String           @id @default(uuid()) @db.Uuid
  cycle_start_date    DateTime
  cycle_end_date      DateTime
  status              String?
  hour_count          Int?
  total_cost          Float?
  total_official_cost Float?
  cycle_no            Int?
  created_at          DateTime         @default(now())
  updated_at          DateTime         @default(now())
  project_usages      project_usages[]

  @@index([cycle_start_date, cycle_end_date])
}
