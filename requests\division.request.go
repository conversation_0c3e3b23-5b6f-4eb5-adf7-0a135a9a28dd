package requests

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type DivisionCreate struct {
	core.BaseValidator
	NameTh      *string `json:"name_th"`
	NameEn      *string `json:"name_en"`
	ShortNameTh *string `json:"short_name_th"`
	ShortNameEn *string `json:"short_name_en"`
}

func (r *DivisionCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.NameTh, "name_th"))
	r.Must(r.Is<PERSON>trRequired(r.NameEn, "name_en"))

	r.Must(r.IsStrUnique(ctx, r.NameTh, models.Division{}.TableName(), "name_th", "", "name_th"))
	r.Must(r.<PERSON>(ctx, r.Name<PERSON>n, models.Division{}.TableName(), "name_en", "", "name_en"))
	r.Must(r.IsStrUnique(ctx, r.ShortNameTh, models.Division{}.TableName(), "short_name_th", "", "short_name_th"))
	r.Must(r.IsStrUnique(ctx, r.ShortNameEn, models.Division{}.TableName(), "short_name_en", "", "short_name_en"))

	return r.Error()
}

type DivisionUpdate struct {
	core.BaseValidator
	NameTh      *string `json:"name_th"`
	NameEn      *string `json:"name_en"`
	ShortNameTh *string `json:"short_name_th"`
	ShortNameEn *string `json:"short_name_en"`
}

func (r *DivisionUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldNameTh := ""
	oldNameEn := ""
	oldShortNameTh := ""
	oldShortNameEn := ""
	division, _ := repo.Division(cc).FindOne("id = ?", cc.Param("id"))
	if division != nil {
		oldNameTh = division.NameTh
		if division.NameEn != nil {
			oldNameEn = utils.ToNonPointer(division.NameEn)
		}
		if division.ShortNameTh != nil {
			oldShortNameTh = utils.ToNonPointer(division.ShortNameTh)
		}
		if division.ShortNameEn != nil {
			oldShortNameEn = utils.ToNonPointer(division.ShortNameEn)
		}
	}

	if utils.ToNonPointer(r.NameTh) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameTh, models.Division{}.TableName(), "name_th", oldNameTh, "name_th"))
	}

	if utils.ToNonPointer(r.NameEn) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameEn, models.Division{}.TableName(), "name_en", oldNameEn, "name_en"))
	}

	if utils.ToNonPointer(r.ShortNameTh) != "" {
		r.Must(r.IsStrUnique(ctx, r.ShortNameTh, models.Division{}.TableName(), "short_name_th", oldShortNameTh, "short_name_th"))
	}

	if utils.ToNonPointer(r.ShortNameEn) != "" {
		r.Must(r.IsStrUnique(ctx, r.ShortNameEn, models.Division{}.TableName(), "short_name_en", oldShortNameEn, "short_name_en"))
	}

	return r.Error()
}
