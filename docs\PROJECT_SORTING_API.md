# Project Sorting API Documentation

## Overview
The Project Pagination API now supports advanced sorting options through the `sort_by` query parameter. This allows you to sort projects by various criteria including project code, budget, usage amount, and usage percentage.

## Authentication
All endpoints require authentication using <PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <your_access_token>
```

## Endpoint

### Get Projects with Sorting
**GET** `/projects`

Retrieves a paginated list of projects with optional sorting.

#### Query Parameters
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `q` (optional): Search query (searches in name, contact name, contact email, and code)
- `sort_by` (optional): Sorting option (see available options below)
- `created_by_id` (optional): Filter by creator user ID
- `status` (optional): Filter by project status (DRAFT, ACTIVE, CLOSED)
- `provider_type` (optional): Filter by provider type (HWC, AWS, CHM)
- `account_holder` (optional): Filter by account holder name

#### Available Sort Options

| Sort Option | Description | Thai Description |
|-------------|-------------|------------------|
| `code_desc` | Project code (newest to oldest) | รหัสโครงการ (ใหม่->เก่า) |
| `code_asc` | Project code (oldest to newest) | รหัสโครงการ (เก่า->ใหม่) |
| `budget_desc` | Budget (high to low) | Budget (มาก->น้อย) |
| `usage_desc` | Usage amount (high to low) | Usage (มาก->น้อย) |
| `usage_percent_desc` | Usage percentage (high to low) | % Usage (มาก->น้อย) |

**Note:** If no `sort_by` parameter is provided, the default sorting is by creation date (newest first).

#### Example Requests

##### Basic Project List (Default Sorting)
```bash
GET /projects?page=1&limit=10
```

##### Sort by Project Code (Descending)
```bash
GET /projects?sort_by=code_desc&page=1&limit=10
```

##### Sort by Budget (High to Low)
```bash
GET /projects?sort_by=budget_desc&page=1&limit=10
```

##### Sort by Usage Amount (High to Low)
```bash
GET /projects?sort_by=usage_desc&page=1&limit=10
```

##### Sort by Usage Percentage (High to Low)
```bash
GET /projects?sort_by=usage_percent_desc&page=1&limit=10
```

##### Combined Filtering and Sorting
```bash
GET /projects?status=ACTIVE&provider_type=AWS&sort_by=budget_desc&page=1&limit=5
```

#### Example Response
```json
{
  "data": [
    {
      "id": "project-uuid-1",
      "name": "Digital Transformation Project",
      "code": "DTP-001",
      "contact_name": "John Doe",
      "contact_phone": "+66-123-456-789",
      "contact_email": "<EMAIL>",
      "budget": 50000.00,
      "status": "ACTIVE",
      "provider_type": "AWS",
      "account_name": "project-account",
      "account_id": "account-123",
      "psa_email": "<EMAIL>",
      "root_account_name": "en3",
      "account_holder": "Finema",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "created_by": {
        "id": "user-uuid",
        "name": "Admin User",
        "email": "<EMAIL>"
      },
      "project_usage": {
        "id": "usage-uuid",
        "amount": 12500.75,
        "official_amount": 12000.00,
        "hour_count": 168,
        "timestamp": "2024-01-15T10:30:00Z",
        "cycle": {
          "id": "cycle-uuid",
          "cycle_start_date": "2024-01-01T00:00:00Z",
          "cycle_end_date": "2024-01-31T23:59:59Z",
          "status": "current"
        }
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "total_pages": 3
  }
}
```

## Sorting Behavior

### Code Sorting
- `code_desc`: Sorts by project code in descending order (Z to A, 9 to 0)
- `code_asc`: Sorts by project code in ascending order (A to Z, 0 to 9)

### Budget Sorting
- `budget_desc`: Sorts by budget amount from highest to lowest
- Projects with null budget values appear at the end

### Usage-Based Sorting
- `usage_desc`: Sorts by the latest usage amount from highest to lowest
- `usage_percent_desc`: Sorts by usage percentage (latest usage / budget * 100) from highest to lowest
- Projects without usage data appear at the end
- For usage percentage, projects with zero or null budget are treated as 0%

## Error Responses

### Invalid Sort Option (400)
```json
{
  "code": "INVALID_SORT_OPTION",
  "message": "Invalid sort option. Valid options are: code_desc, code_asc, budget_desc, usage_desc, usage_percent_desc"
}
```

### Other Error Responses
Standard error responses for authentication, validation, and server errors apply as documented in the main API documentation.

## Performance Considerations

### Usage-Based Sorting
- `usage_desc` and `usage_percent_desc` options require database joins with the project_usage table
- These sorts may be slower for large datasets
- Consider using pagination with reasonable limits when using usage-based sorting

### Recommended Practices
- Use appropriate page limits (10-50 items) for better performance
- Cache results when possible for frequently accessed sorted lists
- Consider the trade-off between real-time data and performance for usage-based sorts

## Usage Examples

### cURL Examples

#### Sort by Project Code (Descending)
```bash
curl -X GET "http://localhost:3001/projects?sort_by=code_desc&page=1&limit=10" \
  -H "Authorization: Bearer your-token"
```

#### Sort by Budget with Status Filter
```bash
curl -X GET "http://localhost:3001/projects?sort_by=budget_desc&status=ACTIVE&page=1&limit=5" \
  -H "Authorization: Bearer your-token"
```

#### Sort by Usage Percentage
```bash
curl -X GET "http://localhost:3001/projects?sort_by=usage_percent_desc&page=1&limit=10" \
  -H "Authorization: Bearer your-token"
```

## Integration Notes

### Frontend Integration
- The sort options can be presented as a dropdown menu with Thai descriptions
- Consider showing loading indicators for usage-based sorts due to potential longer response times
- Implement client-side caching for better user experience

### API Client Libraries
- Validate sort options on the client side before making requests
- Handle the 400 error response for invalid sort options gracefully
- Consider providing helper functions for common sorting scenarios
