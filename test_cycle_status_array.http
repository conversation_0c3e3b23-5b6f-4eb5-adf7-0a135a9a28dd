### Cycle Status Array Filtering API Test File
### This file demonstrates the new array-based status filtering for cycles

@base_url = http://localhost:3001
@auth_token = your_auth_token_here

### Get All Cycles (no status filter)
GET {{base_url}}/cycles?page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Cycles with Single Status (backward compatibility)
GET {{base_url}}/cycles?status=current&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Cycles with Multiple Statuses (new array functionality)
GET {{base_url}}/cycles?status=current,unbilled&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Cycles with Multiple Statuses (with spaces - should be trimmed)
GET {{base_url}}/cycles?status=current, unbilled, billed&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Cycles with Multiple Statuses and Date Range
GET {{base_url}}/cycles?status=current,unbilled&start_date=2024-01-01&end_date=2024-12-31&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Cycles with Multiple Statuses and Search
GET {{base_url}}/cycles?status=current,unbilled&q=2024&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Cycles with All Possible Statuses
GET {{base_url}}/cycles?status=current,unbilled,billed,closed&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Test with Empty Status (should return all cycles)
GET {{base_url}}/cycles?status=&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Test with Non-existent Status
GET {{base_url}}/cycles?status=nonexistent&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Test with Mixed Valid and Invalid Statuses
GET {{base_url}}/cycles?status=current,nonexistent,unbilled&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Expected Behavior:
### - Single status: Works as before (backward compatible)
### - Multiple statuses: Returns cycles that match ANY of the specified statuses (OR operation)
### - Empty status: No filtering applied, returns all cycles
### - Non-existent status: Returns empty result set
### - Whitespace is trimmed from each status value
### - Case-sensitive matching (current != Current)
