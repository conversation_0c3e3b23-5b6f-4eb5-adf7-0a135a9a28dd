/*
  Warnings:

  - You are about to drop the column `organization_Id` on the `projects` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."projects" DROP CONSTRAINT "projects_organization_Id_fkey";

-- AlterTable
ALTER TABLE "public"."projects" DROP COLUMN "organization_Id",
ADD COLUMN     "organization_id" UUID;

-- AddForeignKey
ALTER TABLE "public"."projects" ADD CONSTRAINT "projects_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE SET NULL ON UPDATE CASCADE;
